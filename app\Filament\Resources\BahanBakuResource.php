<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BahanBakuResource\Pages;
use App\Models\BahanBaku;
use Filament\Forms\Components\{Grid, Section, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\{Action, BulkActionGroup, DeleteAction, DeleteBulkAction, EditAction};
use Filament\Tables\Columns\{IconColumn, TextColumn};
use Filament\Infolists\Components\TextEntry;
use Filament\Tables\Table;

class BahanBakuResource extends Resource
{
    protected static ?string $slug = 'bahan-baku';
    public static function getSlug(): string { return 'bahan-baku'; }
    protected static ?string $model = BahanBaku::class;
    protected static ?string $navigationIcon = 'heroicon-o-cube';
    protected static ?string $modelLabel = 'Bahan Baku';
    protected static ?string $pluralModelLabel = 'Bahan Baku';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Bahan Baku')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('nama')
                                    ->label('Nama Bahan')
                                    ->required()
                                    ->maxLength(255)
                                    ->live(debounce: '500ms')
                                    ->formatStateUsing(fn ($state) => ucwords(strtolower($state)))
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if (empty($state)) return;

                                        // Convert to title case first
                                        $state = ucwords(strtolower($state));
                                        $set('nama', $state);

                                        $words = explode(' ', strtoupper($state));
                                        $prefix = '';

                                        if (count($words) === 1) {
                                            // For single word, get first letter and first consonant
                                            $word = $words[0];
                                            $prefix = substr($word, 0, 1); // First letter

                                            // Find first consonant after first letter
                                            $consonants = preg_replace('/[AIUEO]/', '', substr($word, 1));
                                            if (strlen($consonants) > 0) {
                                                $prefix .= substr($consonants, 0, 1);
                                            } else {
                                                // If no consonants found, use second letter
                                                $prefix .= substr($word, 1, 1);
                                            }
                                        } else {
                                            // For multiple words, get first letter of each word
                                            foreach ($words as $word) {
                                                if (strlen($word) > 0) {
                                                    $prefix .= substr($word, 0, 1);
                                                }
                                            }
                                        }

                                        // Ensure prefix is exactly 2 characters
                                        $prefix = substr($prefix . 'BB', 0, 2);

                                        // Get latest number for this prefix
                                        $latestBahanBaku = BahanBaku::where('kode', 'LIKE', $prefix . '%')
                                            ->latest()
                                            ->first();

                                        $sequence = 1;
                                        if ($latestBahanBaku) {
                                            $lastSequence = (int) substr($latestBahanBaku->kode, -2);
                                            $sequence = $lastSequence + 1;
                                        }

                                        $set('kode', $prefix . str_pad($sequence, 2, '0', STR_PAD_LEFT));
                                    })
                                    ->columnSpan(1),
                                TextInput::make('kode')
                                    ->label('Kode Bahan')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->disabled()
                                    ->dehydrated()
                                    ->placeholder('')
                                    ->columnSpan(1),
                            ]),
                    ]),

                Section::make('Bobot Weighted Moving Average')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('bobot_wma_1')
                                    ->label('Bobot Periode 1 (Terbaru)')
                                    ->numeric()
                                    ->default(0.5)
                                    ->step(0.1)
                                    ->minValue(0)
                                    ->rules([
                                        'regex:/^\d{1}(\.\d{1})?$/',
                                        'min:0',
                                        function ($get) {
                                            return function (string $attribute, $value, \Closure $fail) use ($get) {
                                                $bobot1 = (float)$value;
                                                $bobot2 = (float)$get('bobot_wma_2');
                                                $bobot3 = (float)$get('bobot_wma_3');
                                                if (($bobot1 + $bobot2 + $bobot3) > 1) {
                                                    $fail('Total ketiga bobot WMA tidak boleh melebihi 1.');
                                                }
                                            };
                                        }
                                    ])
                                    ->required()
                                    ->columnSpan(1),
                                TextInput::make('bobot_wma_2')
                                    ->label('Bobot Periode 2')
                                    ->numeric()
                                    ->default(0.3)
                                    ->step(0.1)
                                    ->minValue(0)
                                    ->rules([
                                        'regex:/^\d{1}(\.\d{1})?$/',
                                        'min:0',
                                        function ($get) {
                                            return function (string $attribute, $value, \Closure $fail) use ($get) {
                                                $bobot1 = (float)$get('bobot_wma_1');
                                                $bobot2 = (float)$value;
                                                $bobot3 = (float)$get('bobot_wma_3');
                                                if (($bobot1 + $bobot2 + $bobot3) > 1) {
                                                    $fail('Total ketiga bobot WMA tidak boleh melebihi 1.');
                                                }
                                            };
                                        }
                                    ])
                                    ->required()
                                    ->columnSpan(1),
                                TextInput::make('bobot_wma_3')
                                    ->label('Bobot Periode 3 (Terlama)')
                                    ->numeric()
                                    ->default(0.2)
                                    ->step(0.1)
                                    ->minValue(0)
                                    ->rules([
                                        'regex:/^\d{1}(\.\d{1})?$/',
                                        'min:0',
                                        function ($get) {
                                            return function (string $attribute, $value, \Closure $fail) use ($get) {
                                                $bobot1 = (float)$get('bobot_wma_1');
                                                $bobot2 = (float)$get('bobot_wma_2');
                                                $bobot3 = (float)$value;
                                                if (($bobot1 + $bobot2 + $bobot3) > 1) {
                                                    $fail('Total ketiga bobot WMA tidak boleh melebihi 1.');
                                                }
                                            };
                                        }
                                    ])
                                    ->required()
                                    ->columnSpan(1),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('kode')
                    ->label('Kode Bahan')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('nama')
                    ->label('Nama Bahan')
                    ->searchable()
                    ->sortable(),
                IconColumn::make('view_bobot_wma')
                    ->label('Bobot WMA')
                    ->icon('heroicon-o-eye')
                    ->boolean()
                    ->state(true)
                    ->action(
                        Action::make('viewBobotWma')
                            ->icon(null)
                            ->label('')
                            ->iconButton()
                            ->modalHeading(fn (BahanBaku $record) => 'Bobot WMA ' . $record->nama)
                            ->modalSubmitAction(false)
                            ->modalCancelAction(false)
                            ->modalWidth('md')
                            ->infolist([
                                TextEntry::make('bobot_wma_1')
                                    ->label('Bobot Periode 1 (Terbaru)'),
                                TextEntry::make('bobot_wma_2')
                                    ->label('Bobot Periode 2'),
                                TextEntry::make('bobot_wma_3')
                                    ->label('Bobot Periode 3 (Terlama)'),
                            ])
                    ),
            ])
            ->actions([
                EditAction::make()
                    ->hidden(),
                DeleteAction::make()
                    ->label('Hapus')
                    ->modalHeading('Hapus Data Bahan Baku')
                    ->modalDescription('Apakah Anda yakin ingin menghapus data bahan baku ini?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->modalCancelActionLabel('Batal'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus yang dipilih')
                        ->modalHeading('Hapus Data Bahan Baku')
                        ->modalDescription('Apakah Anda yakin ingin menghapus data bahan baku yang dipilih?')
                        ->modalSubmitActionLabel('Ya, Hapus')
                        ->modalCancelActionLabel('Batal'),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBahanBakus::route('/'),
            'create' => Pages\CreateBahanBaku::route('/create'),
            'edit' => Pages\EditBahanBaku::route('/{record}/edit'),
        ];
    }
}