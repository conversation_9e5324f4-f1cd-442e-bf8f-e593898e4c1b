<?php

namespace App\Filament\Resources\ProdukResource\Pages;

use App\Filament\Resources\ProdukResource;
use App\Models\Produksi;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateProduk extends CreateRecord
{
    protected static string $resource = ProdukResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        return 'Tambah Data Produk';
    }

    protected function getCreateFormAction(): Actions\Action
    {
        return parent::getCreateFormAction()
            ->label('Simpan');
    }

    protected function getCreateAnotherFormAction(): Actions\Action
    {
        return parent::getCreateAnotherFormAction()
            ->hidden();
    }

    protected function getCancelFormAction(): Actions\Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }

    protected function afterCreate(): void
    {
        // Get all unique periods from existing production records
        $existingPeriods = Produksi::select('periode')
            ->distinct()
            ->orderBy('periode')
            ->pluck('periode');

        // Create production records with 0 value for all existing periods
        foreach ($existingPeriods as $periode) {
            Produksi::firstOrCreate([
                'produk_id' => $this->record->id,
                'periode' => $periode,
            ], [
                'jumlah' => 0,
                'satuan' => 'pcs'
            ]);
        }
    }
}