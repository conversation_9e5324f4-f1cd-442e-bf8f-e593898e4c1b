<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PenjualanResource\Pages;
use App\Models\{Penjualan, Produk};
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\{Collection, HtmlString, Number};
use Filament\Forms\{Form, Get, Set, Components};
use Filament\Forms\Components\{DatePicker, Hidden, Placeholder, Repeater, Select, TextInput};
use Filament\Infolists\{Infolist, Components as InfolistComponents};
use Filament\Resources\Resource;
use Filament\Support\RawJs;
use Filament\Tables\{Table, Actions, Columns, Filters};

class PenjualanResource extends Resource
{
    protected static ?string $slug = 'penjualan';
    public static function getSlug(): string { return 'penjualan'; }
    protected static ?string $model = Penjualan::class;
    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';
    protected static ?string $modelLabel = 'Penjualan';
    protected static ?string $pluralModelLabel = 'Penjualan';
    protected static ?int $navigationSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Components\Group::make()
                    ->schema([
                        Components\Section::make('Detail Pesanan')
                            ->schema([
                                TextInput::make('kode_pesanan')
                                    ->label('Kode Pesanan')
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->default(function () {
                                        $date = now();
                                        $prefix = 'PO';
                                        $dateCode = $date->format('Ymd');

                                        $latestOrder = Penjualan::whereDate('tanggal_pesanan', $date)
                                            ->orderBy('kode_pesanan', 'desc')
                                            ->first();

                                        $sequence = 1;
                                        if ($latestOrder) {
                                            if (preg_match('/(\d{2})$/', $latestOrder->kode_pesanan, $matches)) {
                                                $lastSequence = (int) $matches[1];
                                                $sequence = $lastSequence + 1;
                                            }
                                        }

                                        return $prefix . $dateCode . str_pad($sequence, 2, '0', STR_PAD_LEFT);
                                    })
                                    ->live(onBlur: true)
                                    ->disabled()
                                    ->dehydrated(),
                                DatePicker::make('tanggal_pesanan')
                                    ->label('Tanggal')
                                    ->required()
                                    ->default(now())
                                    ->displayFormat('d F Y')
                                    ->native(false)
                                    ->live()
                                    ->afterStateUpdated(function ($state, Set $set) {
                                        if (!$state) return;
                                        try {
                                            $date = \Carbon\Carbon::parse($state);
                                            $prefix = 'PO';
                                            $dateCode = $date->format('Ymd');

                                            $latestOrder = Penjualan::whereDate('tanggal_pesanan', $date)
                                                ->orderBy('kode_pesanan', 'desc')
                                                ->first();

                                            $sequence = 1;
                                            if ($latestOrder) {
                                                if (preg_match('/(\d{2})$/', $latestOrder->kode_pesanan, $matches)) {
                                                    $lastSequence = (int) $matches[1];
                                                    $sequence = $lastSequence + 1;
                                                }
                                            }

                                            $set('kode_pesanan', $prefix . $dateCode . str_pad($sequence, 2, '0', STR_PAD_LEFT));
                                        } catch (\Exception $e) {
                                            logger()->error("Error parsing date for kode_pesanan: " . $e->getMessage());
                                        }
                                    }),

                                TextInput::make('nama_pelanggan')
                                    ->label('Nama Pelanggan')
                                    ->required()
                                    ->maxLength(255),

                                Select::make('status_pesanan')
                                    ->label('Status')
                                    ->options([
                                        'menunggu' => 'Menunggu',
                                        'dikirim' => 'Dikirim',
                                        'selesai' => 'Selesai',
                                        'dibatalkan' => 'Dibatalkan',
                                    ])
                                    ->default('menunggu')
                                    ->required()
                                    ->selectablePlaceholder(false),

                            ])->columns(2),

                        Components\Section::make('Produk')
                            ->schema([
                                Repeater::make('produks')
                                    ->label('')
                                    ->schema([
                                        Select::make('produk_id')
                                            ->label('Nama Produk')
                                            ->options(Produk::where('status', 'Aktif')->pluck('nama_produk', 'id'))
                                            ->required()
                                            ->reactive()
                                            ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                                $produk = Produk::find($state);
                                                // Ensure harga is stored as a plain number
                                                $harga = $produk?->harga ?? 0;
                                                $set('harga', $harga); // Set the raw numeric value

                                                // Update subtotal for the current item
                                                $jumlah = $get('jumlah') ?? 1;
                                                // Ensure calculation uses the raw numeric harga
                                                $subtotal = $harga * (is_numeric($jumlah) ? (int)$jumlah : 0);
                                                $set('subtotal', $subtotal); // Set the raw numeric subtotal

                                                // Recalculate and set the total price for the entire order
                                                $set('../../total_harga', self::calculateTotal($get('../../produks')));
                                            })
                                            ->distinct()
                                            ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                            ->searchable()
                                            ->columnSpan(['md' => 3]),

                                        TextInput::make('harga')
                                            ->label('Harga')
                                            ->numeric()
                                            ->prefix('Rp')
                                            ->mask(RawJs::make('$money($input, \',\', \'.\', 0)'))
                                            ->stripCharacters('.')
                                            ->readOnly()
                                            ->dehydrated()
                                            ->columnSpan(['md' => 2]),

                                        TextInput::make('jumlah')
                                            ->label('Jumlah')
                                            ->required()
                                            ->numeric()
                                            ->minValue(1)
                                            ->default(1)
                                            ->reactive()
                                            ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                                // Get the raw harga value, potentially cleaning it if needed
                                                $hargaRaw = $get('harga');
                                                // Clean the value: remove 'Rp' prefix and '.' separators before casting
                                                $hargaClean = preg_replace('/[^\d]/', '', $hargaRaw);
                                                $harga = is_numeric($hargaClean) ? (float)$hargaClean : 0;

                                                $jumlah = is_numeric($state) ? (int)$state : 0;
                                                $subtotal = $harga * $jumlah;
                                                $set('subtotal', $subtotal); // Set the raw numeric subtotal

                                                // Recalculate and set the total price for the entire order
                                                $set('../../total_harga', self::calculateTotal($get('../../produks')));
                                             })
                                            ->columnSpan(['md' => 2]),

                                        TextInput::make('subtotal')
                                            ->label('Subtotal')
                                            ->numeric()
                                            ->prefix('Rp')
                                            ->mask(RawJs::make('$money($input, \',\', \'.\', 0)'))
                                            ->stripCharacters('.')
                                            ->readOnly()
                                            ->dehydrated()
                                            ->columnSpan(['md' => 3]),

                                    ])
                                    ->columns(['md' => 10])
                                    ->addActionLabel('Tambah Produk')
                                    ->live()
                                    ->deleteAction(
                                        fn (Components\Actions\Action $action) => $action->label('Hapus Produk'),
                                    )
                                    ->reorderable(false)
                                    ->defaultItems(1)
                                    // Add reactive() to the Repeater itself to trigger total update on add/delete
                                    ->reactive()
                                    ->afterStateUpdated(function (Get $get, Set $set) {
                                        // Recalculate total whenever repeater items change
                                        $set('total_harga', self::calculateTotal($get('produks')));
                                    }),

                                TextInput::make('total_harga')
                                    ->label('Total Harga')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->mask(RawJs::make('$money($input, \',\', \'.\', 0)'))
                                    ->stripCharacters('.')
                                    ->readOnly()
                                    ->dehydrated()
                                    ->default(function (Get $get): float {
                                        return self::calculateTotal($get('produks'));
                                    }),
                            ]),
                    ])->columnSpan(['lg' => 3]),
            ])->columns(3);
    }

    // Modify calculateTotal to ensure it handles potentially formatted numbers from the state
    public static function calculateTotal(?array $products): float
    {
        if (!$products) {
            return 0;
        }

        return collect($products)->sum(function ($item) {
            // Clean harga and jumlah values retrieved from the state
            $hargaRaw = $item['harga'] ?? 0;
            $jumlahRaw = $item['jumlah'] ?? 0;

            // Clean harga: remove any non-digit characters
            $hargaClean = preg_replace('/[^\d]/', '', $hargaRaw);
            $harga = is_numeric($hargaClean) ? (float)$hargaClean : 0;

            // Ensure jumlah is numeric
            $jumlah = is_numeric($jumlahRaw) ? (int)$jumlahRaw : 0;

            return $harga * $jumlah;
        });
    }

    public static function formatCurrency(float $amount): string
    {
        return 'Rp' . Number::format($amount, precision: 0, locale: 'id');
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Columns\TextColumn::make('kode_pesanan')
                    ->label('Kode Pesanan')
                    ->searchable(),

                Columns\TextColumn::make('nama_pelanggan')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),

                Columns\TextColumn::make('tanggal_pesanan')
                    ->label('Tanggal')
                    ->date('d F Y')
                    ->sortable()
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where(function (Builder $subQuery) use ($search) {
                            // Original English month/year/day search
                            $subQuery->orWhereRaw("DATE_FORMAT(tanggal_pesanan, '%d %M %Y') LIKE ?", ['%' . $search . '%'])
                                     ->orWhereRaw("DATE_FORMAT(tanggal_pesanan, '%d %b %Y') LIKE ?", ['%' . $search . '%'])
                                     ->orWhere('tanggal_pesanan', 'like', '%' . $search . '%'); // Fallback for YYYY-MM-DD or other direct date formats

                            // Indonesian month search logic
                            $indonesianMonths = [
                                'januari' => 1, 'februari' => 2, 'maret' => 3, 'april' => 4,
                                'mei' => 5, 'juni' => 6, 'juli' => 7, 'agustus' => 8,
                                'september' => 9, 'oktober' => 10, 'november' => 11, 'desember' => 12,
                            ];
                            $searchLower = strtolower(trim($search));
                            $parts = preg_split('/\s+/', $searchLower, -1, PREG_SPLIT_NO_EMPTY);

                            $dayNumber = null;
                            $monthNumber = null;
                            $yearNumber = null;

                            // Attempt to parse various Indonesian date formats
                            if (count($parts) === 1) {
                                if (is_numeric($parts[0])) {
                                    if (strlen($parts[0]) === 4) $yearNumber = $parts[0]; // YYYY
                                    // else if (strlen($parts[0]) <= 2) $dayNumber = $parts[0]; // dd (less useful alone)
                                } else {
                                    $monthNumber = $indonesianMonths[$parts[0]] ?? null; // Month
                                }
                            } elseif (count($parts) === 2) {
                                // Month YYYY
                                if (isset($indonesianMonths[$parts[0]]) && is_numeric($parts[1]) && strlen($parts[1]) === 4) {
                                    $monthNumber = $indonesianMonths[$parts[0]];
                                    $yearNumber = $parts[1];
                                }
                                // dd Month
                                elseif (is_numeric($parts[0]) && strlen($parts[0]) <=2 && isset($indonesianMonths[$parts[1]])) {
                                   $dayNumber = $parts[0];
                                   $monthNumber = $indonesianMonths[$parts[1]];
                                }
                            } elseif (count($parts) === 3) {
                                // dd Month YYYY
                                if (is_numeric($parts[0]) && strlen($parts[0]) <= 2 && isset($indonesianMonths[$parts[1]]) && is_numeric($parts[2]) && strlen($parts[2]) === 4) {
                                    $dayNumber = $parts[0];
                                    $monthNumber = $indonesianMonths[$parts[1]];
                                    $yearNumber = $parts[2];
                                }
                            }

                            if ($dayNumber && $monthNumber && $yearNumber) {
                                $subQuery->orWhere(function (Builder $q) use ($dayNumber, $monthNumber, $yearNumber) {
                                    $q->whereDay('tanggal_pesanan', $dayNumber)
                                      ->whereMonth('tanggal_pesanan', $monthNumber)
                                      ->whereYear('tanggal_pesanan', $yearNumber);
                                });
                            } elseif ($monthNumber && $yearNumber) {
                                $subQuery->orWhere(function (Builder $q) use ($monthNumber, $yearNumber) {
                                    $q->whereMonth('tanggal_pesanan', $monthNumber)
                                      ->whereYear('tanggal_pesanan', $yearNumber);
                                });
                            } elseif ($dayNumber && $monthNumber) { // Search for day and month (e.g., "17 Agustus")
                                $subQuery->orWhere(function (Builder $q) use ($dayNumber, $monthNumber) {
                                    $q->whereDay('tanggal_pesanan', $dayNumber)
                                      ->whereMonth('tanggal_pesanan', $monthNumber);
                                });
                            } elseif ($monthNumber) {
                                $subQuery->orWhereMonth('tanggal_pesanan', $monthNumber);
                            } elseif ($yearNumber) {
                                $subQuery->orWhereYear('tanggal_pesanan', $yearNumber);
                            }
                        });
                    }),

                Columns\TextColumn::make('total_harga')
                    ->label('Total Harga')
                    ->state(function (Penjualan $record): float {
                        return $record->total_harga;
                    })
                    ->formatStateUsing(fn (string $state): string => self::formatCurrency((float) $state))
                    ->sortable(),

                Columns\SelectColumn::make('status_pesanan')
                    ->label('Status')
                    ->options([
                        'menunggu' => 'Menunggu',
                        'dikirim' => 'Dikirim',
                        'selesai' => 'Selesai',
                        'dibatalkan' => 'Dibatalkan',
                    ])
                    ->selectablePlaceholder(false),
            ])
            ->filters([
                Filters\SelectFilter::make('status_pesanan')
                    ->label('Status Pesanan')
                    ->options([
                        'menunggu' => 'Menunggu',
                        'dikirim' => 'Dikirim',
                        'selesai' => 'Selesai',
                        'dibatalkan' => 'Dibatalkan',
                    ]),
            ])
            ->actions([
                Actions\Action::make('detail')
                    ->label('Detail')
                    ->icon('heroicon-o-eye')
                    ->modalContent(function (Penjualan $record) {
                        $record->loadMissing('produks');
                        $produks = $record->produks;
                        $totalKeseluruhan = $record->total_harga;

                        $html = '<div class="overflow-x-auto">';
                        $html .= '<table class="w-full divide-y divide-gray-200 dark:divide-gray-700">';
                        $html .= '<thead class="bg-gray-50 dark:bg-gray-800">';
                        $html .= '<tr>';
                        $html .= '<th scope="col" class="lg:px-6 md:px-4 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Nama Produk</th>';
                        $html .= '<th scope="col" class="lg:px-6 md:px-4 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Harga</th>';
                        $html .= '<th scope="col" class="lg:px-6 md:px-4 px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Jumlah</th>';
                        $html .= '<th scope="col" class="lg:px-6 md:px-4 px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Subtotal</th>';
                        $html .= '</tr>';
                        $html .= '</thead>';
                        $html .= '<tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">';

                        if ($produks->isEmpty()) {
                            $html .= '<tr><td colspan="4" class="lg:px-6 md:px-4 px-2 py-4 whitespace-nowrap text-sm text-gray-500 text-center dark:text-gray-400">Tidak ada produk dalam pesanan ini.</td></tr>';
                        } else {
                            foreach ($produks as $index => $produk) {
                                $html .= '<tr>';
                                $html .= '<td class="lg:px-6 md:px-4 px-2 py-4 text-sm text-gray-900 dark:text-white">' . e($produk->nama_produk) . '</td>';
                                $html .= '<td class="lg:px-6 md:px-4 px-2 py-4 whitespace-nowrap text-sm text-gray-500 text-left dark:text-gray-400">' . self::formatCurrency($produk->pivot->harga) . '</td>';
                                $html .= '<td class="lg:px-6 md:px-4 px-2 py-4 whitespace-nowrap text-sm text-gray-500 text-center dark:text-gray-400">' . $produk->pivot->jumlah . '</td>';
                                $html .= '<td class="lg:px-6 md:px-4 px-2 py-4 whitespace-nowrap text-sm text-gray-500 text-right dark:text-gray-400">' . self::formatCurrency($produk->pivot->subtotal) . '</td>';
                                $html .= '</tr>';
                            }
                        }

                        $html .= '</tbody>';
                        $html .= '<tfoot class="bg-gray-50 dark:bg-gray-800">';
                        $html .= '<tr>';
                        $html .= '<td colspan="3" class="lg:px-6 md:px-4 px-2 py-3 text-right text-sm font-medium text-gray-900 dark:text-white">Total</td>';
                        $html .= '<td class="lg:px-6 md:px-4 px-2 py-3 text-right text-sm font-medium text-gray-900 dark:text-white">' . self::formatCurrency($totalKeseluruhan) . '</td>';
                        $html .= '</tr>';
                        $html .= '</tfoot>';
                        $html .= '</table></div>';

                        return new HtmlString($html);
                    })
                    ->modalHeading(fn (Penjualan $record) => 'Detail Penjualan: ' . $record->kode_pesanan)
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->modalWidth('xl'),
                Actions\Action::make('cetakPdf')
                    ->label('Cetak')
                    ->icon('heroicon-o-printer')
                    ->url(fn (Penjualan $record): string => route('penjualan.pdf', ['penjualan' => $record->id]))
                    ->openUrlInNewTab(),
                Actions\EditAction::make()
                    ->hidden(),
                Actions\DeleteAction::make()
                    ->label('Hapus')
                    ->modalHeading('Hapus Data Penjualan')
                    ->modalDescription('Apakah Anda yakin ingin menghapus data penjualan ini?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->modalCancelActionLabel('Batal'),
            ])
            ->bulkActions([
                Actions\BulkActionGroup::make([
                    Actions\DeleteBulkAction::make()
                        ->label('Hapus yang dipilih')
                        ->modalHeading('Hapus Data Penjualan')
                        ->modalDescription('Apakah Anda yakin ingin menghapus data penjualan yang dipilih?')
                        ->modalSubmitActionLabel('Ya, Hapus')
                        ->modalCancelActionLabel('Batal'),
                ]),
            ])
            ->defaultSort('tanggal_pesanan', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPenjualans::route('/'),
            'create' => Pages\CreatePenjualan::route('/create'),
            'edit' => Pages\EditPenjualan::route('/{record}/edit'),
        ];
    }
}
