<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProdukResource\Pages;
use App\Models\{BahanBaku, Produk};
use Filament\Forms\Form;
use Filament\Forms\Components\{Grid, Section, Select, TextInput};
use Filament\Infolists\Components\TextEntry;
use Filament\Resources\Resource;
use Filament\Support\RawJs;
use Filament\Tables\Table;
use Filament\Tables\Actions\{Action, BulkActionGroup, DeleteAction, DeleteBulkAction, EditAction};
use Filament\Tables\Columns\{IconColumn, TextColumn};
use Filament\Tables\Filters\SelectFilter;

class ProdukResource extends Resource
{
    protected static ?string $model = Produk::class;
    protected static ?string $navigationIcon = 'heroicon-o-cake';
    protected static ?string $modelLabel = 'Produk';
    protected static ?string $pluralModelLabel = 'Produk';
    protected static ?string $slug = 'produk';
    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Produk')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('nama_produk')
                                    ->label('Nama Produk')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpan(1),
                                Select::make('status')
                                    ->label('Status')
                                    ->options([
                                        'Aktif' => 'Aktif',
                                        'Nonaktif' => 'Nonaktif',
                                    ])
                                    ->default('Aktif')
                                    ->extraInputAttributes(['tabindex' => '-1'])
                                    ->required()
                                    ->selectablePlaceholder(false)
                                    ->columnSpan(1),
                            ]),
                        TextInput::make('harga')
                            ->label('Harga')
                            ->required()
                            ->numeric()
                            ->prefix('Rp')
                            ->mask(RawJs::make('$money($input, \',\', \'.\', 0)'))
                            ->stripCharacters('.')
                            ->minValue(0),
                        Select::make('bahanBakus')
                            ->label('Bahan Baku')
                            ->relationship('bahanBakus', 'nama')
                            ->multiple()
                            ->preload()
                            ->searchable()
                            ->required(),
                    ]),
                Section::make('Bobot Weighted Moving Average')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('bobot_wma_1')
                                    ->label('Bobot Periode 1 (Terbaru)')
                                    ->numeric()
                                    ->default(0.5)
                                    ->step(0.1)
                                    ->minValue(0)
                                    ->rules([
                                        'regex:/^\d{1}(\.\d{1})?$/',
                                        'min:0',
                                        function ($get) {
                                            return function (string $attribute, $value, \Closure $fail) use ($get) {
                                                $bobot1 = (float)$value;
                                                $bobot2 = (float)$get('bobot_wma_2');
                                                $bobot3 = (float)$get('bobot_wma_3');
                                                if (($bobot1 + $bobot2 + $bobot3) > 1) {
                                                    $fail('Total ketiga bobot WMA tidak boleh melebihi 1.');
                                                }
                                            };
                                        }
                                    ])
                                    ->required()
                                    ->columnSpan(1),
                                TextInput::make('bobot_wma_2')
                                    ->label('Bobot Periode 2')
                                    ->numeric()
                                    ->default(0.3)
                                    ->step(0.1)
                                    ->minValue(0)
                                    ->rules([
                                        'regex:/^\d{1}(\.\d{1})?$/',
                                        'min:0',
                                        function ($get) {
                                            return function (string $attribute, $value, \Closure $fail) use ($get) {
                                                $bobot1 = (float)$get('bobot_wma_1');
                                                $bobot2 = (float)$value;
                                                $bobot3 = (float)$get('bobot_wma_3');
                                                if (($bobot1 + $bobot2 + $bobot3) > 1) {
                                                    $fail('Total ketiga bobot WMA tidak boleh melebihi 1.');
                                                }
                                            };
                                        }
                                    ])
                                    ->required()
                                    ->columnSpan(1),
                                TextInput::make('bobot_wma_3')
                                    ->label('Bobot Periode 3 (Terlama)')
                                    ->numeric()
                                    ->default(0.2)
                                    ->step(0.1)
                                    ->minValue(0)
                                    ->rules([
                                        'regex:/^\d{1}(\.\d{1})?$/',
                                        'min:0',
                                        function ($get) {
                                            return function (string $attribute, $value, \Closure $fail) use ($get) {
                                                $bobot1 = (float)$get('bobot_wma_1');
                                                $bobot2 = (float)$get('bobot_wma_2');
                                                $bobot3 = (float)$value;
                                                if (($bobot1 + $bobot2 + $bobot3) > 1) {
                                                    $fail('Total ketiga bobot WMA tidak boleh melebihi 1.');
                                                }
                                            };
                                        }
                                    ])
                                    ->required()
                                    ->columnSpan(1),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nama_produk')
                    ->label('Nama Produk')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('harga')
                    ->label('Harga')
                    ->formatStateUsing(fn (string $state): string => 'Rp' . number_format((int) $state, 0, ',', '.'))
                    ->sortable(),
                IconColumn::make('view_bahan_baku')
                    ->label('Bahan Baku')
                    ->icon('heroicon-o-eye')
                    ->boolean()
                    ->state(fn (Produk $record): bool => $record->bahanBakus()->exists())
                    ->action(
                        Action::make('viewBahanBaku')
                            ->icon(null)
                            ->label('')
                            ->iconButton()
                            ->modalHeading(fn (Produk $record) => 'Bahan Baku ' . $record->nama_produk)
                            ->modalSubmitAction(false)
                            ->modalCancelAction(false)
                            ->modalWidth('md')
                            ->infolist([
                                TextEntry::make('bahanBakus.nama')
                                    ->label('')
                                    ->bulleted()
                                    ->placeholder('Tidak ada bahan baku.')
                            ])
                    ),
                IconColumn::make('view_bobot_wma')
                    ->label('Bobot WMA')
                    ->icon('heroicon-o-eye')
                    ->boolean()
                    ->state(true)
                    ->action(
                        Action::make('viewBobotWma')
                            ->icon(null)
                            ->label('')
                            ->iconButton()
                            ->modalHeading(fn (Produk $record) => 'Bobot WMA ' . $record->nama_produk)
                            ->modalSubmitAction(false)
                            ->modalCancelAction(false)
                            ->modalWidth('md')
                            ->infolist([
                                TextEntry::make('bobot_wma_1')
                                    ->label('Bobot Periode 1 (Terbaru)'),
                                TextEntry::make('bobot_wma_2')
                                    ->label('Bobot Periode 2'),
                                TextEntry::make('bobot_wma_3')
                                    ->label('Bobot Periode 3 (Terlama)'),
                            ])
                    ),
                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Aktif' => 'success',
                        'Nonaktif' => 'danger',
                    }),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'Aktif' => 'Aktif',
                        'Nonaktif' => 'Nonaktif',
                    ])
            ])
            ->actions([
                EditAction::make()
                    ->hidden(),
                DeleteAction::make()
                    ->label('Hapus')
                    ->modalHeading('Hapus Data Produk')
                    ->modalDescription('Apakah Anda yakin ingin menghapus data produk ini?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->modalCancelActionLabel('Batal'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus yang dipilih')
                        ->modalHeading('Hapus Data Produk')
                        ->modalDescription('Apakah Anda yakin ingin menghapus data produk yang dipilih?')
                        ->modalSubmitActionLabel('Ya, Hapus')
                        ->modalCancelActionLabel('Batal'),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProduks::route('/'),
            'create' => Pages\CreateProduk::route('/create'),
            'edit' => Pages\EditProduk::route('/{record}/edit'),
        ];
    }
}