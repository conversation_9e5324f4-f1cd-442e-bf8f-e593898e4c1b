<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StokResource\Pages;
use App\Models\Stok;
use Illuminate\Database\Eloquent\{Builder, Collection};
use Filament\Forms\{Form, Components};
use Filament\Resources\Resource;
use Filament\Support\RawJs;
use Filament\Tables\{Table, Actions, Columns, Filters};

class StokResource extends Resource
{
    protected static ?string $slug = 'stok';
    public static function getSlug(): string { return 'stok'; }
    protected static ?string $model = Stok::class;
    protected static ?string $navigationIcon = 'heroicon-o-archive-box';
    protected static ?string $modelLabel = 'Stok';
    protected static ?string $pluralModelLabel = 'Stok';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        $bahanBakus = \App\Models\BahanBaku::all();

        return $form->schema([
            Components\DatePicker::make('periode')
                ->label('Periode')
                ->required()
                ->format('Y-m')
                ->displayFormat('F Y')
                ->native(false)
                ->columnSpanFull(),
            Components\Section::make('Data Stok Bahan Baku')
                ->schema(
                    $bahanBakus->chunk(2)->flatMap(function ($chunk) {
                        return [
                            Components\Grid::make()
                                ->schema(
                                    $chunk->flatMap(fn ($bahanBaku) => [
                                        Components\Section::make($bahanBaku->nama)
                                            ->compact()
                                            ->collapsible(false)
                                            ->extraAttributes([
                                                'class' => 'text-center',
                                            ])
                                            ->schema([
                                                Components\Grid::make()
                                                    ->schema([
                                                        Components\TextInput::make("stok_data.{$bahanBaku->id}.jumlah")
                                                            ->label(false)
                                                            ->numeric()
                                                            ->step('0.1') // Ditambahkan untuk UX input desimal
                                                            ->default(0.0) // Diubah ke 0.0
                                                            ->inlineLabel()
                                                            ->extraAttributes([
                                                                'class' => 'flex items-center justify-center',
                                                            ])
                                                            ->dehydrateStateUsing(fn ($state) => $state !== null ? (float)str_replace(',', '.', (string)$state) : 0.0) // Disesuaikan untuk menangani input koma dan memastikan float
                                                            ->afterStateHydrated(function ($component, $state, $record) use ($bahanBaku) {
                                                                if ($record) {
                                                                    $existingStok = \App\Models\Stok::where('periode', $record->periode)
                                                                        ->where('bahan_baku_id', $bahanBaku->id)
                                                                        ->first();

                                                                    $component->state($existingStok?->jumlah ?? 0.0); // Diubah ke 0.0
                                                                }
                                                            }),
                                                        Components\Select::make("stok_data.{$bahanBaku->id}.satuan")
                                                            ->label(false)
                                                            ->options([
                                                                'kg' => 'Kilogram',
                                                                'g' => 'Gram',
                                                                'l' => 'Liter',
                                                                'ml' => 'Mililiter',
                                                                'pcs' => 'Pcs',
                                                            ])
                                                            ->default('kg')
                                                            ->selectablePlaceholder(false)
                                                            ->inlineLabel()
                                                            ->extraAttributes([
                                                                'class' => 'flex items-center justify-center',
                                                            ])
                                                            ->afterStateHydrated(function ($component, $state, $record) use ($bahanBaku) {
                                                                if ($record) {
                                                                    $existingStok = \App\Models\Stok::where('periode', $record->periode)
                                                                        ->where('bahan_baku_id', $bahanBaku->id)
                                                                        ->first();

                                                                    $component->state($existingStok?->satuan ?? 'kg');
                                                                }
                                                            }),
                                                        Components\TextInput::make("stok_data.{$bahanBaku->id}.harga")
                                                            ->label(false)
                                                            ->prefix('Rp')
                                                            ->numeric()
                                                            ->mask(RawJs::make('$money($input, \',\', \'.\', 0)'))
                                                            ->stripCharacters('.')
                                                            ->minValue(0)
                                                            ->default(0)
                                                            ->inlineLabel()
                                                            ->extraAttributes([
                                                                'class' => 'flex items-center justify-center',
                                                            ])
                                                            ->afterStateHydrated(function ($component, $state, $record) use ($bahanBaku) {
                                                                if ($record) {
                                                                    $existingStok = \App\Models\Stok::where('periode', $record->periode)
                                                                        ->where('bahan_baku_id', $bahanBaku->id)
                                                                        ->first();
                                                                    $component->state($existingStok?->harga ?? 0);
                                                                }
                                                            }),
                                                    ])
                                            ])
                                            ->columnSpan(2)
                                    ])->toArray()
                                )->columns(4)
                        ];
                    })->toArray()
                )->columns(1)
        ])->statePath('data');
    }

    public static function table(Table $table): Table
    {
        $bahanBakus = \App\Models\BahanBaku::all();

        $columns = [
            Columns\TextColumn::make('periode')
                ->label('Periode')
                ->date('F Y')
                ->sortable()
                ->searchable(query: function (Builder $query, string $search): Builder {
                    return $query->where(function (Builder $subQuery) use ($search) {
                        // Original English month/year search
                        $subQuery->orWhereRaw("DATE_FORMAT(periode, '%M %Y') LIKE ?", ["%{$search}%"])
                                 ->orWhereRaw("DATE_FORMAT(periode, '%Y') LIKE ?", ["%{$search}%"]);

                        // Indonesian month search logic
                        $indonesianMonths = [
                            'januari' => 1, 'februari' => 2, 'maret' => 3, 'april' => 4,
                            'mei' => 5, 'juni' => 6, 'juli' => 7, 'agustus' => 8,
                            'september' => 9, 'oktober' => 10, 'november' => 11, 'desember' => 12,
                        ];
                        $searchLower = strtolower(trim($search));
                        $parts = preg_split('/\s+/', $searchLower, -1, PREG_SPLIT_NO_EMPTY);

                        $monthNumber = null;
                        $yearNumber = null;

                        if (count($parts) === 1) {
                            if (is_numeric($parts[0]) && strlen($parts[0]) === 4) {
                                $yearNumber = $parts[0];
                            } else {
                                $monthNumber = $indonesianMonths[$parts[0]] ?? null;
                            }
                        } elseif (count($parts) === 2) {
                            // Try "Month Year"
                            if (isset($indonesianMonths[$parts[0]]) && is_numeric($parts[1]) && strlen($parts[1]) === 4) {
                                $monthNumber = $indonesianMonths[$parts[0]];
                                $yearNumber = $parts[1];
                            }
                            // Try "Year Month"
                            elseif (is_numeric($parts[0]) && strlen($parts[0]) === 4 && isset($indonesianMonths[$parts[1]])) {
                                $yearNumber = $parts[0];
                                $monthNumber = $indonesianMonths[$parts[1]];
                            }
                        }

                        if ($monthNumber && $yearNumber) {
                            $subQuery->orWhere(function (Builder $q) use ($monthNumber, $yearNumber) {
                                $q->whereMonth('periode', $monthNumber)
                                  ->whereYear('periode', $yearNumber);
                            });
                        } elseif ($monthNumber) {
                            $subQuery->orWhereMonth('periode', $monthNumber);
                        } elseif ($yearNumber) {
                            $subQuery->orWhereYear('periode', $yearNumber);
                        }
                    });
                }),
        ];

        foreach ($bahanBakus as $bahanBaku) {
            $columns[] = Columns\TextColumn::make("bahan_baku_{$bahanBaku->id}")
                ->label($bahanBaku->nama)
                ->getStateUsing(function ($record) use ($bahanBaku) {
                    $stok = \App\Models\Stok::where('periode', $record->periode)
                        ->where('bahan_baku_id', $bahanBaku->id)
                        ->first();
                    if ($stok) {
                        $jumlah = $stok->jumlah;
                        if (fmod($jumlah, 1) == 0) {
                            $jumlah_formatted = number_format($jumlah, 0);
                        } else {
                            $jumlah_formatted = number_format($jumlah, 1);
                        }
                        return "{$jumlah_formatted} {$stok->satuan}";
                    } else {
                        $defaultSatuan = $bahanBaku->satuan ?? 'kg';
                        return "0 {$defaultSatuan}";
                    }
                })
                ->numeric();
        }

        return $table
            ->columns($columns)
            ->filters([
                Filters\Filter::make('periode')
                    ->form([
                        Components\DatePicker::make('periode_dari')
                            ->label('Dari Periode')
                            ->format('Y-m')
                            ->displayFormat('F Y')
                            ->native(false),
                        Components\DatePicker::make('periode_sampai')
                            ->label('Sampai Periode')
                            ->format('Y-m')
                            ->displayFormat('F Y')
                            ->native(false),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['periode_dari'],
                                fn (Builder $query, $date): Builder => $query->whereDate('periode', '>=', $date),
                            )
                            ->when(
                                $data['periode_sampai'],
                                fn (Builder $query, $date): Builder => $query->whereDate('periode', '<=', $date),
                            );
                    })
            ])
            ->actions([
                Actions\EditAction::make()
                    ->hidden(),
                Actions\DeleteAction::make()
                    ->label('Hapus')
                    ->modalHeading('Hapus Data Stok')
                    ->modalDescription('Apakah Anda yakin ingin menghapus data stok ini?')
                    ->modalSubmitActionLabel('Ya, Hapus')
                    ->modalCancelActionLabel('Batal')
                    ->action(function ($record) {
                        \App\Models\Stok::where('periode', $record->periode)->forceDelete();
                    }),
            ])
            ->bulkActions([
                Actions\BulkActionGroup::make([
                    Actions\DeleteBulkAction::make()
                        ->label('Hapus yang dipilih')
                        ->modalHeading('Hapus Data Stok')
                        ->modalDescription('Apakah Anda yakin ingin menghapus data stok yang dipilih?')
                        ->modalSubmitActionLabel('Ya, Hapus')
                        ->modalCancelActionLabel('Batal')
                        ->action(function (Collection $records) {
                            foreach ($records as $record) {
                                \App\Models\Stok::where('periode', $record->periode)->forceDelete();
                            }
                        }),
                ]),
            ])
            ->modifyQueryUsing(function ($query) {
                return $query->selectRaw('MIN(id) as id, periode')
                    ->groupBy('periode');
            })
            ->defaultSort('periode', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStoks::route('/'),
            'create' => Pages\CreateStok::route('/create'),
            'edit' => Pages\EditStok::route('/{record}/edit'),
        ];
    }
}